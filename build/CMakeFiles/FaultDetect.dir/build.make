# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/fault_detect

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/fault_detect/build

# Include any dependencies generated for this target.
include CMakeFiles/FaultDetect.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/FaultDetect.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/FaultDetect.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/FaultDetect.dir/flags.make

CMakeFiles/FaultDetect.dir/src/common_simple.cpp.o: CMakeFiles/FaultDetect.dir/flags.make
CMakeFiles/FaultDetect.dir/src/common_simple.cpp.o: /home/<USER>/fault_detect/src/common_simple.cpp
CMakeFiles/FaultDetect.dir/src/common_simple.cpp.o: CMakeFiles/FaultDetect.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/fault_detect/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/FaultDetect.dir/src/common_simple.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/FaultDetect.dir/src/common_simple.cpp.o -MF CMakeFiles/FaultDetect.dir/src/common_simple.cpp.o.d -o CMakeFiles/FaultDetect.dir/src/common_simple.cpp.o -c /home/<USER>/fault_detect/src/common_simple.cpp

CMakeFiles/FaultDetect.dir/src/common_simple.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/FaultDetect.dir/src/common_simple.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/fault_detect/src/common_simple.cpp > CMakeFiles/FaultDetect.dir/src/common_simple.cpp.i

CMakeFiles/FaultDetect.dir/src/common_simple.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/FaultDetect.dir/src/common_simple.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/fault_detect/src/common_simple.cpp -o CMakeFiles/FaultDetect.dir/src/common_simple.cpp.s

CMakeFiles/FaultDetect.dir/src/test_simple.cpp.o: CMakeFiles/FaultDetect.dir/flags.make
CMakeFiles/FaultDetect.dir/src/test_simple.cpp.o: /home/<USER>/fault_detect/src/test_simple.cpp
CMakeFiles/FaultDetect.dir/src/test_simple.cpp.o: CMakeFiles/FaultDetect.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/fault_detect/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/FaultDetect.dir/src/test_simple.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/FaultDetect.dir/src/test_simple.cpp.o -MF CMakeFiles/FaultDetect.dir/src/test_simple.cpp.o.d -o CMakeFiles/FaultDetect.dir/src/test_simple.cpp.o -c /home/<USER>/fault_detect/src/test_simple.cpp

CMakeFiles/FaultDetect.dir/src/test_simple.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/FaultDetect.dir/src/test_simple.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/fault_detect/src/test_simple.cpp > CMakeFiles/FaultDetect.dir/src/test_simple.cpp.i

CMakeFiles/FaultDetect.dir/src/test_simple.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/FaultDetect.dir/src/test_simple.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/fault_detect/src/test_simple.cpp -o CMakeFiles/FaultDetect.dir/src/test_simple.cpp.s

# Object files for target FaultDetect
FaultDetect_OBJECTS = \
"CMakeFiles/FaultDetect.dir/src/common_simple.cpp.o" \
"CMakeFiles/FaultDetect.dir/src/test_simple.cpp.o"

# External object files for target FaultDetect
FaultDetect_EXTERNAL_OBJECTS =

bin/FaultDetect: CMakeFiles/FaultDetect.dir/src/common_simple.cpp.o
bin/FaultDetect: CMakeFiles/FaultDetect.dir/src/test_simple.cpp.o
bin/FaultDetect: CMakeFiles/FaultDetect.dir/build.make
bin/FaultDetect: CMakeFiles/FaultDetect.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/fault_detect/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable bin/FaultDetect"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/FaultDetect.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/FaultDetect.dir/build: bin/FaultDetect
.PHONY : CMakeFiles/FaultDetect.dir/build

CMakeFiles/FaultDetect.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/FaultDetect.dir/cmake_clean.cmake
.PHONY : CMakeFiles/FaultDetect.dir/clean

CMakeFiles/FaultDetect.dir/depend:
	cd /home/<USER>/fault_detect/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/fault_detect /home/<USER>/fault_detect /home/<USER>/fault_detect/build /home/<USER>/fault_detect/build /home/<USER>/fault_detect/build/CMakeFiles/FaultDetect.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/FaultDetect.dir/depend

